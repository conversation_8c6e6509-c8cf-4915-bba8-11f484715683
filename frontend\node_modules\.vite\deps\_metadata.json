{"hash": "2bdc4c32", "configHash": "3f566533", "lockfileHash": "179cf6be", "browserHash": "c4dd2f1d", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "b9be9a55", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "1728be55", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "cd00c833", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "575b51ad", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "5cb61793", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "083f15a2", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "a5eb3d8c", "needsInterop": false}}, "chunks": {"chunk-NYHBQTAG": {"file": "chunk-NYHBQTAG.js"}, "chunk-P5XWQMHZ": {"file": "chunk-P5XWQMHZ.js"}}}